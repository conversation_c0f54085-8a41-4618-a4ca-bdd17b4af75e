'use client';

import { useState, useEffect, useRef } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Plus } from 'lucide-react';

export default function TagInput({
  defaultTags = [],
  onChange,
  placeholder = 'Add tags...',
  maxTags = 10,
  className = '',
  multiple = true,
  queryKey = ['tags'],
  queryFn,
  preloadedTags = [],
  isBucket = false,
}) {
  // Convert defaultTags to array of strings for display
  const initialTags = defaultTags.map(tag =>
    typeof tag === 'object' ? tag.name || tag.label : tag
  );

  const [tags, setTags] = useState(initialTags);
  const [input, setInput] = useState('');
  const [showSuggestions, setShowSuggestions] = useState(false);
  const inputRef = useRef(null);
  const suggestionsRef = useRef(null);

  // Fetch suggestions if queryFn is provided
  const { data: fetchedTags } = useQuery(
    queryFn && queryKey
      ? {
          queryKey,
          queryFn,
        }
      : {
          queryKey: ['no-query'],
          queryFn: () => null,
          enabled: false,
        }
  );

  // Use either fetched tags or preloaded tags
  const allTags = queryFn ? fetchedTags : preloadedTags;

  // Filter suggestions based on input
  const suggestions = (allTags || [])
    .filter(tag => {
      const tagName = typeof tag === 'object' ? tag.name || tag.label : tag;
      const searchTerm = input.toLowerCase();
      const currentTags = tags.map(t => t.toLowerCase());
      return tagName.toLowerCase().includes(searchTerm) &&
        (!multiple ? true : !currentTags.includes(tagName.toLowerCase()));
    })
    .slice(0, 5);

  // Check if current input matches any existing tag
  const inputMatchesExisting = input.trim() && suggestions.some(tag => {
    const tagName = typeof tag === 'object' ? tag.name || tag.label : tag;
    return tagName.toLowerCase() === input.trim().toLowerCase();
  });

  useEffect(() => {
    // Handle clicks outside suggestions dropdown
    function handleClickOutside(event) {
      if (suggestionsRef.current && !suggestionsRef.current.contains(event.target)) {
        setShowSuggestions(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const addTag = (tag, isNew = false) => {
    const tagObj = typeof tag === 'object' ? tag : { name: tag, label: tag };
    const tagName = tagObj.name || tagObj.label;
    const trimmedTag = tagName.trim();

    if (trimmedTag) {
      const newTags = multiple
        ? [...tags, trimmedTag].slice(0, maxTags)
        : [trimmedTag];
      setTags(newTags);
      setInput('');

      // For buckets, return an object structure
      if (isBucket && !multiple) {
        const bucketData = !isNew && allTags.find(t => (t.name || t.label) === trimmedTag);
        onChange?.(bucketData ? { value: bucketData.id, label: trimmedTag } : { label: trimmedTag, isNew: true });
      } else if (multiple) {
        // For multiple tags, return array of strings with isNew flag for new tags
        onChange?.(newTags.map(t => {
          const existingTag = allTags.find(et => (et.name || et.label) === t);
          return existingTag || { label: t, isNew: true };
        }));
      } else {
        // For single tag, return the tag name with isNew flag if it's new
        onChange?.(isNew ? { label: trimmedTag, isNew: true } : trimmedTag);
      }
    }
  };

  const removeTag = (indexToRemove) => {
    const newTags = tags.filter((_, index) => index !== indexToRemove);
    setTags(newTags);
    if (isBucket && !multiple) {
      onChange?.(null);
    } else {
      onChange?.(multiple ? newTags : null);
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      if (suggestions.length && showSuggestions) {
        addTag(suggestions[0]);
      } else if (input.trim()) {
        addTag(input, true);
      }
    } else if (e.key === 'Backspace' && !input) {
      removeTag(tags.length - 1);
    } else if (e.key === 'Escape') {
      setShowSuggestions(false);
    } else if (e.key === 'ArrowDown' && showSuggestions && (suggestions.length || input.trim())) {
      e.preventDefault();
      const suggestionElements = suggestionsRef.current?.querySelectorAll('button');
      suggestionElements?.[0]?.focus();
    }
  };

  return (
    <div className={`relative ${className}`}>
      <div className="flex flex-wrap gap-2 p-2 bg-base-100 rounded-lg border border-base-300 focus-within:border-primary">
        {tags.map((tag, index) => (
          <span
            key={index}
            className="badge badge-primary gap-1"
          >
            {tag}
            <button
              type="button"
              onClick={() => removeTag(index)}
              className="btn btn-ghost btn-xs px-1"
            >
              ×
            </button>
          </span>
        ))}

        <input
          ref={inputRef}
          type="text"
          value={input}
          onChange={(e) => {
            setInput(e.target.value);
            setShowSuggestions(true);
          }}
          onFocus={() => setShowSuggestions(true)}
          onKeyDown={handleKeyDown}
          placeholder={(!multiple && tags.length === 1) || (multiple && tags.length >= maxTags) ? '' : placeholder}
          className="flex-1 min-w-[120px] bg-transparent border-none outline-none placeholder:text-base-content/50"
          disabled={(!multiple && tags.length === 1) || (multiple && tags.length >= maxTags)}
        />
      </div>

      {/* Suggestions dropdown */}
      {showSuggestions && (suggestions.length > 0 || input.trim()) && (
        <div
          ref={suggestionsRef}
          className="absolute z-10 w-full mt-1 bg-base-100 rounded-lg shadow-lg border border-base-300"
        >
          {suggestions.map((suggestion, index) => {
            const suggestionName = typeof suggestion === 'object'
              ? suggestion.name || suggestion.label
              : suggestion;
            return (
              <button
                key={suggestionName}
                type="button"
                onClick={() => {
                  addTag(suggestion);
                  setShowSuggestions(false);
                  inputRef.current?.focus();
                }}
                onKeyDown={(e) => {
                  if (e.key === 'ArrowDown') {
                    e.preventDefault();
                    const elements = suggestionsRef.current?.querySelectorAll('button');
                    const nextIndex = index + 1;
                    if (nextIndex < elements.length) {
                      elements[nextIndex].focus();
                    }
                  } else if (e.key === 'ArrowUp') {
                    e.preventDefault();
                    if (index === 0) {
                      inputRef.current?.focus();
                    } else {
                      const elements = suggestionsRef.current?.querySelectorAll('button');
                      elements[index - 1].focus();
                    }
                  }
                }}
                className="w-full px-4 py-2 text-left hover:bg-base-200 focus:bg-base-200 focus:outline-none"
              >
                {suggestionName}
              </button>
            );
          })}

          {/* Create new option */}
          {input.trim() && !inputMatchesExisting && (
            <button
              type="button"
              onClick={() => {
                addTag(input, true);
                setShowSuggestions(false);
                inputRef.current?.focus();
              }}
              className="w-full px-4 py-2 text-left hover:bg-base-200 focus:bg-base-200 focus:outline-none text-primary flex items-center gap-2"
            >
              <Plus className="w-4 h-4" />
              Create &quot;{input.trim()}&quot;
            </button>
          )}
        </div>
      )}
    </div>
  );
}
