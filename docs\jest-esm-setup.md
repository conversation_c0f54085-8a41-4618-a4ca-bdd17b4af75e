# Jest ES Modules Configuration

This document explains how <PERSON><PERSON> is configured in SubsKeepr to support ES modules (ESM) testing.

## Problem

Jest traditionally uses CommonJS modules, but modern JavaScript/TypeScript projects often use ES modules. When testing code that imports ES modules, <PERSON><PERSON> throws errors like:

```
SyntaxError: Cannot use import statement outside a module
```

## Solution

We've configured <PERSON><PERSON> to support ES modules using the following approach:

### 1. Dependencies

Install the required dependencies:

```bash
npm install --save-dev jest ts-jest @types/jest
```

### 2. Jest Configuration (`jest.config.js`)

```javascript
const path = require("path");

module.exports = {
  testEnvironment: "node",
  moduleDirectories: ["node_modules", "<rootDir>"],
  rootDir: path.join(__dirname),
  moduleNameMapper: {
    "^@/(.*)$": "<rootDir>/$1",
  },
  setupFiles: ["<rootDir>/jest.setup.js"],
  collectCoverageFrom: ["libs/**/*.js", "utils/**/*.js", "!**/node_modules/**"],
  
  // ES Modules support configuration
  preset: "ts-jest/presets/default-esm",
  extensionsToTreatAsEsm: [".ts", ".tsx"],
  globals: {
    "ts-jest": {
      useESM: true,
    },
  },
  
  transform: {
    "^.+\\.(js|jsx)$": "babel-jest",
    "^.+\\.(ts|tsx)$": "ts-jest",
  },
  
  // Handle ES modules in node_modules
  transformIgnorePatterns: [
    "node_modules/(?!(string-strip-html|lodash-es|other-esm-packages)/)",
  ],
  
  moduleFileExtensions: ["js", "jsx", "ts", "tsx", "json"],
};
```

### 3. Package.json Scripts

Update the test scripts to use Node.js experimental VM modules:

```json
{
  "scripts": {
    "test": "node --experimental-vm-modules node_modules/jest/bin/jest.js",
    "test:watch": "node --experimental-vm-modules node_modules/jest/bin/jest.js --watch"
  }
}
```

### 4. TypeScript Configuration

Ensure your `tsconfig.json` supports ES modules:

```json
{
  "compilerOptions": {
    "module": "esnext",
    "moduleResolution": "bundler",
    "target": "es5",
    "allowJs": true,
    "esModuleInterop": true,
    "skipLibCheck": true
  }
}
```

### 5. Mocking ES Modules

When testing code that imports ES modules, mock them at the top of your test files:

```javascript
// Mock problematic ES modules
jest.mock("string-strip-html", () => ({
  stripHtml: jest.fn((input) => ({ result: input })),
}));

jest.mock("@/libs/email", () => ({
  sendEmail: jest.fn(),
}));
```

## Key Points

1. **Experimental Feature**: ES modules support in Jest is still experimental and requires the `--experimental-vm-modules` flag.

2. **Transform Ignore Patterns**: Add ES module packages to `transformIgnorePatterns` so Jest can transform them.

3. **Windows Compatibility**: Use the full node command path instead of NODE_OPTIONS environment variable for Windows compatibility.

4. **Mock Early**: Mock ES modules at the top of test files before any imports.

## Testing the Configuration

Run tests to verify the configuration works:

```bash
npm test
```

You should see tests running without ES module import errors.

## Troubleshooting

- If you get "Cannot use import statement" errors, check that the problematic package is included in `transformIgnorePatterns`.
- If mocks aren't working, ensure they're defined before any imports in your test files.
- For Windows users, make sure to use the full node command path in package.json scripts.
